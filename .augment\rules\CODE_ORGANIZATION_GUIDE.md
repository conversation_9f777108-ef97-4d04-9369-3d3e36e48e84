---
type: "always_apply"
---

# 代码组织开发指南

## 📋 系统架构概述

### 技术栈
- **前端**: Vue.js 3 (Composition API) + Tailwind CSS + Axios
- **后端**: Node.js + Express.js + SQLite + JWT认证
- **架构模式**: MVC分层架构 + 组件化前端
- **权限系统**: RBAC基于角色的访问控制

### 核心设计原则
- **模块化开发**: 每个功能独立模块，高内聚低耦合
- **本地化资源**: 禁用CDN依赖，所有资源本地化
- **统一规范**: 标准化的文件组织、命名和开发流程

## 🎯 前端开发规范

### 文件组织结构
```
frontend/
├── pages/                    # 页面HTML文件
│   ├── [功能模块]/           # 功能模块目录 (kebab-case)
│   │   └── [页面名].html     # 页面文件 (kebab-case)
│   ├── dashboard.html        # 主页
│   ├── index.html            # 入口页面
│   └── login.html            # 登录页面
├── scripts/                  # 页面脚本和工具
│   ├── pages/                # 页面逻辑脚本
│   │   └── [功能模块]/       # 对应页面模块
│   │       └── [页面名].js   # 页面逻辑 (kebab-case)
│   ├── api/                  # API调用封装
│   ├── common/               # 通用工具和初始化
│   ├── utils/                # 工具函数
│   ├── config.js             # 配置文件
│   └── global.js             # 全局脚本
├── components/               # Vue组件
│   ├── [功能模块]/           # 功能分组 (kebab-case)
│   │   └── [组件名].js       # 组件文件 (PascalCase)
│   ├── common/               # 通用组件
│   └── ui/                   # UI组件
├── assets/                   # 静态资源
│   ├── css/                  # 全局样式
│   ├── images/               # 图片资源
│   ├── fonts/                # 字体文件
│   └── svg/                  # SVG图标
├── css/                      # 功能专用样式
│   └── [功能模块].css        # 功能样式文件
└── js/libs/                  # 本地化第三方库
```

### Vue.js 3 组件开发规范

#### 1. 组件结构标准
- **必须使用**: Composition API + `setup()` 函数
- **响应式数据**: 使用 `ref()` 和 `reactive()`
- **计算属性**: 使用 `computed()`
- **生命周期**: 使用 `onMounted()`, `onUnmounted()` 等

#### 2. 组件命名规范
- **组件文件**: PascalCase (如 `ApplicationForm.js`)
- **组件目录**: kebab-case (如 `application/`)
- **页面文件**: kebab-case (如 `new-application.html`)

#### 3. 组件通信规范
- **Props**: 使用 `props` 参数接收父组件数据
- **Events**: 使用 `emit()` 向父组件发送事件
- **全局状态**: 通过 `sessionStorage` 或全局事件

### 页面开发模式
1. **HTML页面**: 定义基础结构和Vue挂载点
2. **页面脚本**: 实现页面业务逻辑和数据处理
3. **组件引用**: 通过模块化方式引入所需组件
4. **样式文件**: 功能专用CSS文件

## 🔧 后端开发规范

### 文件组织结构
```
backend/
├── routes/                   # 路由定义
│   ├── [功能]Routes.js       # 功能路由 (camelCase)
│   └── index.js              # 路由汇总注册
├── controllers/              # 控制器层
│   └── [功能]Controller.js   # 控制器 (camelCase)
├── services/                 # 业务服务层
│   └── [功能]Service.js      # 服务 (camelCase)
├── middlewares/              # 中间件
│   ├── auth.js               # 认证中间件
│   ├── [功能]Upload.js       # 文件上传中间件
│   └── [其他].js             # 其他中间件
├── database/                 # 数据库相关
│   ├── database.js           # 数据库连接和初始化
│   ├── [功能]Repository.js   # 数据访问层
│   └── migrations/           # 数据库迁移脚本
├── models/                   # 数据模型
│   └── [功能]Model.js        # 数据模型定义
├── modules/                  # 独立功能模块
│   └── [功能名]/             # 模块目录 (如warehouse)
│       ├── index.js          # 模块入口和路由
│       ├── controllers/      # 模块控制器
│       ├── services/         # 模块服务
│       └── [其他]/           # 模块特定功能
├── config/                   # 配置文件
│   └── index.js              # 统一配置管理
├── utils/                    # 工具函数
├── algorithms/               # AI算法模块
├── uploads/                  # 文件上传目录
└── server.js                 # 服务器入口
```

### API设计规范

#### 1. RESTful API标准
- **GET**: 获取资源 `/api/[resource]` 或 `/api/[resource]/:id`
- **POST**: 创建资源 `/api/[resource]`
- **PUT**: 更新资源 `/api/[resource]/:id`
- **DELETE**: 删除资源 `/api/[resource]/:id`

#### 2. 统一响应格式
```javascript
// 成功响应
{ success: true, data: {...}, message: "操作成功" }

// 错误响应
{ success: false, message: "错误描述", error: "ERROR_CODE" }
```

#### 3. 控制器开发模式
- **参数验证**: 检查必填字段和数据格式
- **业务逻辑**: 调用对应的service层方法
- **错误处理**: 统一的try-catch和错误响应
- **日志记录**: 关键操作和错误信息记录

### 中间件使用规范
- **认证中间件**: `authenticateJWT` - 验证用户身份
- **权限中间件**: `checkPermission(permission)` - 检查用户权限
- **文件上传**: 使用对应的upload中间件
- **错误处理**: 统一的错误处理中间件

## 🗄️ 数据库设计规范

### 表结构规范
- **主键**: 使用TEXT类型的UUID
- **时间字段**: 统一使用 `created_at`, `updated_at`
- **状态字段**: 使用枚举值，如 `active`, `inactive`
- **JSON字段**: 复杂数据使用TEXT存储JSON字符串

### 字段命名规范
- **表名**: 复数形式，下划线分隔 (如 `applications`)
- **字段名**: 下划线分隔 (如 `user_id`, `created_at`)
- **外键**: `[表名]_id` 格式 (如 `user_id`)

## 🔐 权限系统规范

### 权限定义规范
- **权限ID**: `{功能模块}_{操作}` (如 `product_view`, `schedule_create`)
- **权限分组**: 相同功能模块归入同一权限组
- **权限类型**: view(查看)、create(创建)、edit(编辑)、delete(删除)、manage(管理)

### 权限检查机制
- **后端API**: 路由中使用 `checkPermission(permission)` 中间件
- **前端页面**: 页面加载时检查用户权限
- **UI元素**: 根据权限动态显示/隐藏功能按钮

### 新功能权限添加流程
1. 在 `PermissionManager.js` 中定义权限
2. 在 `PermissionTemplateManager.js` 中同步添加
3. 后端API路由添加权限检查中间件
4. 前端页面和组件添加权限验证

## 🚀 新功能开发流程

### 开发流程标准
1. **需求分析** → 明确功能边界和用户需求
2. **设计阶段** → UI设计 + API接口设计
3. **后端开发** → 数据模型 → 服务层 → 控制器 → 路由
4. **前端开发** → 页面结构 → 组件开发 → 业务逻辑
5. **权限配置** → 权限定义 → 权限检查 → 权限测试
6. **测试验证** → 功能测试 → 权限测试 → 集成测试
7. **文档更新** → README.md + func.md 同步更新

### 开发检查清单
- [ ] 创建独立的功能模块目录
- [ ] 遵循文件命名和组织规范
- [ ] 实现统一的错误处理机制
- [ ] 添加必要的权限控制
- [ ] 编写清晰的代码注释
- [ ] 更新相关文档

## 📝 代码质量标准

### 错误处理规范
- **后端**: 统一使用try-catch，返回标准错误格式
- **前端**: 全局错误捕获 + 用户友好提示
- **日志记录**: 关键操作和错误信息详细记录

### 代码注释规范
- **函数注释**: 说明功能、参数和返回值
- **复杂逻辑**: 添加行内注释说明
- **配置项**: 重要配置添加说明注释

### 性能优化要求
- **前端**: 组件懒加载、合理使用计算属性
- **后端**: 数据库查询优化、适当的缓存机制
- **资源**: 本地化资源管理，避免CDN依赖

## 🔧 部署和维护规范

### 本地化资源管理
- **第三方库**: 必须下载到 `frontend/js/libs/` 目录
- **错误处理**: 每个资源加载都要有失败处理机制
- **版本管理**: 记录库的版本信息和更新历史

### 配置管理规范
- **环境配置**: 使用 `backend/config/index.js` 统一管理
- **敏感信息**: 使用环境变量，不提交到代码库
- **功能开关**: 通过配置控制功能的启用/禁用

---

## 📚 快速参考

### 新页面开发模板
1. 创建 `frontend/pages/[模块]/[页面].html`
2. 创建 `frontend/scripts/pages/[模块]/[页面].js`
3. 如需组件，创建 `frontend/components/[模块]/[组件].js`
4. 如需样式，创建 `frontend/css/[模块].css`

### 新API开发模板
1. 创建 `backend/routes/[功能]Routes.js`
2. 创建 `backend/controllers/[功能]Controller.js`
3. 创建 `backend/services/[功能]Service.js`
4. 如需数据访问，创建 `backend/database/[功能]Repository.js`
5. 在 `backend/routes/index.js` 中注册路由

### 新模块开发模板 (复杂功能)
1. 创建 `backend/modules/[模块名]/` 目录
2. 创建模块入口 `index.js` 定义路由
3. 创建 `controllers/` 和 `services/` 子目录
4. 在主路由文件中引入模块路由

### 权限添加模板
1. 在 `frontend/components/user/PermissionManager.js` 中定义权限
2. 在 `frontend/components/user/PermissionTemplateManager.js` 中同步添加
3. 后端路由添加 `checkPermission(permission)` 中间件
4. 前端页面添加权限检查逻辑
5. UI元素根据权限控制显示

**遵循这些规范，确保代码质量和团队协作效率！** 🎯
