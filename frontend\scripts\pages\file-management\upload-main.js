/**
 * 文件上传主页
 * 提供文件上传类型选择功能
 */

import { createStandardApp } from '../../common/pageInit.js';
import Sidebar from '../../../components/common/Sidebar.js';

createStandardApp({
    components: {
        Sidebar
    },
    setup() {
        const { ref } = Vue;

        // 页面状态
        const sidebarOpen = ref(localStorage.getItem('sidebarOpen') === 'true' || false);

        return {
            sidebarOpen
        };
    },
    requiredPermissions: ['file_upload'],
    onUserLoaded: async (user) => {
        console.log('文件上传主页加载完成，当前用户:', user.username);
    }
}).mount('#app');
