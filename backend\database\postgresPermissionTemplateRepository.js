/**
 * PostgreSQL权限模板数据访问层
 * 替换SQLite的permissionTemplateRepository，适配PostgreSQL数据库
 */

const BasePostgresRepository = require('./basePostgresRepository');
const logger = require('../utils/logger');

class PostgresPermissionTemplateRepository extends BasePostgresRepository {
    constructor() {
        super();
        if (process.env.VERBOSE_LOGS === 'true') {
            logger.info('PostgreSQL权限模板数据访问层初始化完成');
        }
    }

    /**
     * 获取所有权限模板
     */
    async findAll() {
        try {
            const templates = await this.findMany(`
                SELECT * FROM permission_templates
                ORDER BY is_built_in DESC, created_at DESC
            `);
            return templates.map(template => this.transformTemplate(template));
        } catch (error) {
            logger.error('获取所有权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID查找权限模板
     */
    async findById(id) {
        try {
            const template = await this.findOne('SELECT * FROM permission_templates WHERE id = $1', [id]);
            return template ? this.transformTemplate(template) : null;
        } catch (error) {
            logger.error(`根据ID查找权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 根据名称查找权限模板
     */
    async findByName(name) {
        try {
            const template = await this.findOne('SELECT * FROM permission_templates WHERE name = $1', [name]);
            return template ? this.transformTemplate(template) : null;
        } catch (error) {
            logger.error(`根据名称查找权限模板失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 创建新权限模板
     */
    async create(templateData) {
        try {
            const now = new Date().toISOString();
            const id = templateData.id || Date.now().toString();

            const result = await this.query(`
                INSERT INTO permission_templates (
                    id, name, description, permissions, is_built_in, created_at, updated_at
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING *
            `, [
                id,
                templateData.name,
                templateData.description || '',
                JSON.stringify(templateData.permissions || []),
                templateData.isBuiltIn ? true : false,
                now,
                now
            ]);

            return this.transformTemplate(result.rows[0]);
        } catch (error) {
            logger.error('创建权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 更新权限模板
     */
    async update(id, templateData) {
        try {
            const now = new Date().toISOString();

            const result = await this.query(`
                UPDATE permission_templates SET
                    name = $1, description = $2, permissions = $3, updated_at = $4
                WHERE id = $5
                RETURNING *
            `, [
                templateData.name,
                templateData.description || '',
                JSON.stringify(templateData.permissions || []),
                now,
                id
            ]);

            return result.rows.length > 0 ? this.transformTemplate(result.rows[0]) : null;
        } catch (error) {
            logger.error(`更新权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 删除权限模板
     */
    async delete(id) {
        try {
            const result = await this.query('DELETE FROM permission_templates WHERE id = $1', [id]);
            return result.rowCount > 0;
        } catch (error) {
            logger.error(`删除权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 检查模板名称是否已存在
     */
    async checkNameExists(name, excludeId = null) {
        try {
            let query = 'SELECT COUNT(*) as count FROM permission_templates WHERE name = $1';
            let params = [name];
            
            if (excludeId) {
                query += ' AND id != $2';
                params.push(excludeId);
            }

            const result = await this.findOne(query, params);
            return result ? parseInt(result.count) > 0 : false;
        } catch (error) {
            logger.error(`检查模板名称是否存在失败 (${name}):`, error);
            throw error;
        }
    }

    /**
     * 获取内置权限模板
     */
    async findBuiltInTemplates() {
        try {
            const templates = await this.findMany(`
                SELECT * FROM permission_templates
                WHERE is_built_in = true
                ORDER BY created_at DESC
            `);
            return templates.map(template => this.transformTemplate(template));
        } catch (error) {
            logger.error('获取内置权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 获取自定义权限模板
     */
    async findCustomTemplates() {
        try {
            const templates = await this.findMany(`
                SELECT * FROM permission_templates
                WHERE is_built_in = false
                ORDER BY created_at DESC
            `);
            return templates.map(template => this.transformTemplate(template));
        } catch (error) {
            logger.error('获取自定义权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 批量创建权限模板
     */
    async createBatch(templatesData) {
        try {
            const results = [];
            for (const templateData of templatesData) {
                const result = await this.create(templateData);
                results.push(result);
            }
            return results;
        } catch (error) {
            logger.error('批量创建权限模板失败:', error);
            throw error;
        }
    }

    /**
     * 复制权限模板
     */
    async duplicate(id, newName) {
        try {
            const originalTemplate = await this.findById(id);
            if (!originalTemplate) {
                throw new Error('原始模板不存在');
            }

            const duplicateData = {
                name: newName,
                description: `${originalTemplate.description} (副本)`,
                permissions: originalTemplate.permissions,
                isBuiltIn: false
            };

            return await this.create(duplicateData);
        } catch (error) {
            logger.error(`复制权限模板失败 (${id}):`, error);
            throw error;
        }
    }

    /**
     * 获取权限模板统计信息
     */
    async getTemplateStats() {
        try {
            const stats = await this.findOne(`
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN is_built_in = true THEN 1 END) as built_in_count,
                    COUNT(CASE WHEN is_built_in = false THEN 1 END) as custom_count
                FROM permission_templates
            `);
            
            return {
                totalCount: parseInt(stats.total_count) || 0,
                builtInCount: parseInt(stats.built_in_count) || 0,
                customCount: parseInt(stats.custom_count) || 0
            };
        } catch (error) {
            logger.error('获取权限模板统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 转换权限模板数据格式
     */
    transformTemplate(template) {
        if (!template) return null;

        return {
            ...template,
            permissions: this.parseJSON(template.permissions, []),
            isBuiltIn: Boolean(template.is_built_in)
        };
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    }
}

module.exports = PostgresPermissionTemplateRepository;
