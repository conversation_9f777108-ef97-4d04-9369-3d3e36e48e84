<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传选择 - 管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/common.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 加载指示器 -->
    <div id="loading" class="loading-overlay">
        <div class="text-center">
            <div class="loading-spinner mx-auto"></div>
            <p class="mt-4 text-gray-600">正在根据您的权限跳转到相应页面...</p>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="flex min-h-screen" style="display: none;">
        <!-- 侧边栏 -->
        <sidebar
            :user="currentUser"
            :sidebar-open="sidebarOpen"
        ></sidebar>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col md:ml-72">
            <!-- 顶部导航栏 -->
            <header class="bg-white shadow-sm border-b border-gray-200 md:hidden">
                <div class="flex items-center justify-between px-4 py-3">
                    <button
                        @click="sidebarOpen = !sidebarOpen"
                        class="text-gray-600 hover:text-gray-900 focus:outline-none"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900">文件上传选择</h1>
                    <div class="w-6"></div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-4 md:p-6 lg:p-8">
                <div class="max-w-4xl mx-auto">
                    <!-- 页面标题 -->
                    <div class="mb-8 text-center">
                        <h1 class="text-3xl font-bold text-gray-800 mb-4">文件上传中心</h1>
                        <p class="text-gray-600 text-lg">请选择您要上传的文件类型</p>
                    </div>

                    <!-- 上传选项卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 客户二认文件上传 -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col">
                            <div class="p-8 flex-1 flex flex-col">
                                <div class="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mx-auto mb-6">
                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 text-center mb-4">客户二认文件</h3>
                                <p class="text-gray-600 text-center mb-6 flex-1">上传客户产品的二次认证文件，支持版本管理和邮件通知功能</p>
                                <div class="text-center mt-auto">
                                    <a href="/file-upload/certification"
                                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        开始上传
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 样品单上传 -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col">
                            <div class="p-8 flex-1 flex flex-col">
                                <div class="flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mx-auto mb-6">
                                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 text-center mb-4">样品单</h3>
                                <p class="text-gray-600 text-center mb-6 flex-1">填写样品单，便于样品单管理和追踪</p>
                                <div class="text-center mt-auto">
                                    <a href="/file-upload/sample"
                                       class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        开始填写
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 生产控制表 -->
                        <div class="bg-white rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col">
                            <div class="p-8 flex-1 flex flex-col">
                                <div class="flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mx-auto mb-6">
                                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-800 text-center mb-4">生产控制表</h3>
                                <p class="text-gray-600 text-center mb-6 flex-1">填写生产控制表，便于生产过程管理和质量控制</p>
                                <div class="text-center mt-auto">
                                    <a href="/file-upload/production-control"
                                       class="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-lg hover:bg-purple-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        开始填写
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 功能说明 -->
                    <div class="mt-12 bg-blue-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-blue-800 mb-4">功能说明</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <h5 class="font-medium text-blue-700 mb-2">客户二认文件</h5>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 支持多种文件格式（PDF、DOC、XLS、图片等）</li>
                                    <li>• 自动版本管理和变更追踪</li>
                                    <li>• 邮件通知相关人员</li>
                                    <li>• 客户和产品信息管理</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-medium text-blue-700 mb-2">样品单</h5>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 样品单信息填写和管理</li>
                                    <li>• 样品单数据记录和存储</li>
                                    <li>• 样品单状态跟踪</li>
                                    <li>• 即将推出更多功能...</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-medium text-blue-700 mb-2">生产控制表</h5>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 生产过程控制记录</li>
                                    <li>• 质量控制点管理</li>
                                    <li>• 即将推出更多功能...</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 遮罩层（移动端侧边栏） -->
    <div
        v-if="sidebarOpen"
        @click="sidebarOpen = false"
        class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
    ></div>

    <!-- JavaScript 库 -->
    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/file-management/upload-main.js"></script>
</body>
</html>
