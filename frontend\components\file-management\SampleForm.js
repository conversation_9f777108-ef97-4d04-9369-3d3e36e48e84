/**
 * 样品寄送通知单表单组件
 * 用于填写样品寄送通知单信息
 */

const { createApp, ref, reactive, computed, onMounted } = Vue;

export default {
    name: 'SampleForm',
    setup() {
        // 响应式数据
        const formData = reactive({
            // 收件人信息
            to: '',
            cc: '',
            
            // 基本信息
            date: '',
            number: '',
            purpose: '',
            
            // 公司信息
            companyName: '',
            recipient: '',
            application: '',
            address: '',
            
            // 产品规格信息
            productModel: '',
            productDescription: '',
            outerLayer: '',
            middleLayer: '',
            innerLayer: '',
            headband: '',
            noseBridge: '',
            earLoop: '',
            
            // 气阀信息
            valve: 'none', // none, with
            valveTopCover: '',
            valveBottomCover: '',
            valveSheet: '',
            
            // 客制化说明
            quantity: '',
            maskPrintFile: '',
            printColor: '',
            printSize: '',
            headbandColor: '',
            valveColor: '',
            valvePrint: 'none', // none, with
            printMaterial: '',
            printMaterialColor: '',
            printMaterialSize: '',
            
            // 样品内容
            adjustmentBuckle: 'none', // none, with
            internalTest: 'no', // yes, no
            
            // 包装信息
            packagingMethod: '',
            packagingMaterials: [], // 改为数组，支持多选
            colorBox: false, // 彩盒
            colorBoxFile: null, // 彩盒图档
            tape: false, // 胶带
            tapeMaterial: '', // 胶带材质和尺寸
            blisterPack: false, // 泡壳
            blisterPackFile: null, // 吊卡图档
            other: false, // 其他
            otherDescription: '', // 其他描述
            
            // 测试信息
            testNotes: '',
            testQuantity: '',
            testItems: [], // 选中的测试项目
            testLevels: {}, // 测试等级 {项目名: 等级值}
            otherTestItems: {} // 其他项目的自定义内容 {项目名: 自定义内容}
        });

        const loading = ref(false);
        const submitting = ref(false);
        const users = ref([]); // 用户列表
        const loadingUsers = ref(false); // 加载用户状态

        // 计算属性
        const formattedDate = computed(() => {
            if (!formData.date) return '';
            const date = new Date(formData.date);
            return date.toISOString().split('T')[0].replace(/-/g, '.');
        });

        const generatedNumber = computed(() => {
            if (!formData.date) return '';
            const date = new Date(formData.date);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `SD-${year}${month}${day}-V-N`;
        });

        // 获取用户列表
        async function fetchUsers() {
            try {
                loadingUsers.value = true;
                const response = await axios.get('/api/users', {
                    headers: {
                        'Authorization': `Bearer ${sessionStorage.getItem('token')}`
                    }
                });

                if (response.data.success) {
                    users.value = response.data.data || [];
                } else {
                    console.error('获取用户列表失败:', response.data.message);
                }
            } catch (error) {
                console.error('获取用户列表错误:', error);
            } finally {
                loadingUsers.value = false;
            }
        }

        // 生命周期
        onMounted(() => {
            // 设置默认日期为今天
            const today = new Date();
            formData.date = today.toISOString().split('T')[0];

            // 获取用户列表
            fetchUsers();
        });

        // 方法
        function handleInternalTestChange(value) {
            formData.internalTest = value;
            // 如果选择"否"，清空测试相关数据
            if (value === 'no') {
                formData.testItems = [];
                formData.testNotes = '';
                formData.testQuantity = '';
                formData.testLevels = {};
                formData.otherTestItems = {};
                testSampleCounts.value = {};
            }
        }

        // 定义测试项目层级关系
        const testHierarchy = {
            'NIOSH美规': ['一般穿透阻抗', '加载测试', '头带拉力', '气阀拉力', '密合度测试', '其他NIOSH'],
            'CE欧规': ['一般穿透阻抗CE', '加载测试CE', '全方位阻抗CE', '总泄漏率', '头带拉力CE', '气阀拉力CE', '白云石', '其他CE'],
            'AS/NZS澳规': ['一般穿透阻抗澳规', '全方位阻抗澳规', '头带拉力澳规', '气阀拉力澳规', '其他澳规'],
            '日规': ['粉尘和吸气上升值', '湿阻力', '使用时间', '吸气排气阻力测试', '泄漏率', '其他日规'],
            'GB国标': ['一般穿透阻抗GB', '加载测试GB', '头带拉力GB', '气阀拉力GB', '泄漏测试', '其他GB'],
            '医疗项目': ['血透测试', 'BFE', 'PFE', '压差', '阻燃测试', '其他医疗']
        };

        function handleTestItemChange(item, checked) {
            if (checked) {
                if (!formData.testItems.includes(item)) {
                    formData.testItems.push(item);
                }
            } else {
                const index = formData.testItems.indexOf(item);
                if (index > -1) {
                    formData.testItems.splice(index, 1);
                }

                // 如果取消勾选一级选项，同时取消勾选所有二级选项
                if (testHierarchy[item]) {
                    testHierarchy[item].forEach(subItem => {
                        const subIndex = formData.testItems.indexOf(subItem);
                        if (subIndex > -1) {
                            formData.testItems.splice(subIndex, 1);
                        }
                        // 清除测试等级和其他项目内容
                        delete formData.testLevels[subItem];
                        delete formData.otherTestItems[subItem];
                    });
                }

                // 清除当前项目的测试等级和其他项目内容
                delete formData.testLevels[item];
                delete formData.otherTestItems[item];
            }

            // 更新测试样品数量
            updateTestSampleCounts();
        }

        function handleTestLevelChange(item, level) {
            formData.testLevels[item] = level;
        }

        function handleOtherTestChange(item, content) {
            formData.otherTestItems[item] = content;
        }

        function handleFileUpload(type, event) {
            const file = event.target.files[0];
            if (file) {
                if (type === 'colorBox') {
                    formData.colorBoxFile = file;
                } else if (type === 'blisterPack') {
                    formData.blisterPackFile = file;
                }
            }
        }

        // 测试样品数量的响应式数据
        const testSampleCounts = ref({});

        // 获取二级选框对应的一级选框
        function getMainCategory(subItem) {
            for (const [mainCategory, subItems] of Object.entries(testHierarchy)) {
                if (subItems.includes(subItem)) {
                    return mainCategory;
                }
            }
            return '';
        }

        // 监听测试项目变化，自动更新数量
        function updateTestSampleCounts() {
            const newCounts = {};
            formData.testItems.forEach(item => {
                // 跳过一级选框，只计算二级选框的数量
                const isMainCategory = ['NIOSH美规', 'CE欧规', 'AS/NZS澳规', '日规', 'GB国标', '医疗项目'].includes(item);
                if (!isMainCategory) {
                    // 如果已经有设置的数量，保持不变；否则设置默认数量
                    if (testSampleCounts.value[item]) {
                        newCounts[item] = testSampleCounts.value[item];
                    } else {
                        // 根据不同测试项目设置默认数量
                        if (item.includes('穿透阻抗') || item.includes('加载测试')) {
                            newCounts[item] = 10; // 过滤效率测试需要10个
                        } else if (item.includes('拉力')) {
                            newCounts[item] = 5; // 拉力测试需要5个
                        } else if (item.includes('泄漏') || item.includes('密合度')) {
                            newCounts[item] = 3; // 泄漏测试需要3个
                        } else {
                            newCounts[item] = 5; // 其他测试默认5个
                        }
                    }
                }
            });
            testSampleCounts.value = newCounts;
        }

        // 按一级选框分组显示测试数量
        const groupedTestCounts = computed(() => {
            const grouped = {};
            Object.keys(testSampleCounts.value).forEach(item => {
                const mainCategory = getMainCategory(item);
                if (mainCategory) {
                    if (!grouped[mainCategory]) {
                        grouped[mainCategory] = [];
                    }
                    grouped[mainCategory].push({
                        name: item,
                        count: testSampleCounts.value[item]
                    });
                }
            });
            return grouped;
        });

        function updateTestSampleCount(item, count) {
            // 这个函数用于用户手动调整测试数量
            testSampleCounts.value[item] = parseInt(count) || 1;
        }

        function handlePackagingMaterialChange(material, checked) {
            if (checked) {
                if (!formData.packagingMaterials.includes(material)) {
                    formData.packagingMaterials.push(material);
                }
            } else {
                const index = formData.packagingMaterials.indexOf(material);
                if (index > -1) {
                    formData.packagingMaterials.splice(index, 1);
                }
            }
        }

        function validateForm() {
            const requiredFields = [
                'to', 'companyName', 'recipient', 'productModel', 'quantity'
            ];
            
            for (const field of requiredFields) {
                if (!formData[field] || formData[field].trim() === '') {
                    throw new Error(`请填写${getFieldLabel(field)}`);
                }
            }
        }

        function getFieldLabel(field) {
            const labels = {
                'to': '收件人',
                'companyName': '公司名称',
                'recipient': '收件人',
                'productModel': '产品型号',
                'quantity': '数量'
            };
            return labels[field] || field;
        }

        async function submitForm() {
            try {
                submitting.value = true;
                
                // 表单验证
                validateForm();
                
                // 这里可以添加提交到后端的逻辑
                console.log('提交样品单数据:', formData);
                
                // 模拟提交延迟
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                alert('样品寄送通知单提交成功！');
                
                // 可以跳转到列表页面或重置表单
                // window.location.href = '/file-list';
                
            } catch (error) {
                console.error('提交失败:', error);
                alert('提交失败: ' + error.message);
            } finally {
                submitting.value = false;
            }
        }

        function resetForm() {
            Object.keys(formData).forEach(key => {
                if (key === 'testItems' || key === 'packagingMaterials') {
                    formData[key] = [];
                } else if (key === 'testLevels' || key === 'otherTestItems') {
                    formData[key] = {};
                } else if (key === 'date') {
                    const today = new Date();
                    formData[key] = today.toISOString().split('T')[0];
                } else if (key === 'valve' || key === 'adjustmentBuckle') {
                    formData[key] = 'none';
                } else if (key === 'internalTest') {
                    formData[key] = 'no';
                } else if (key === 'valvePrint') {
                    formData[key] = 'none';
                } else if (key === 'colorBox' || key === 'tape' || key === 'blisterPack' || key === 'other') {
                    formData[key] = false;
                } else if (key === 'colorBoxFile' || key === 'blisterPackFile') {
                    formData[key] = null;
                } else if (key === 'tapeMaterial' || key === 'otherDescription') {
                    formData[key] = '';
                } else {
                    formData[key] = '';
                }
            });

            // 重置测试样品数量
            testSampleCounts.value = {};
        }

        return {
            formData,
            loading,
            submitting,
            users,
            loadingUsers,
            formattedDate,
            generatedNumber,
            testSampleCounts,
            groupedTestCounts,
            handleInternalTestChange,
            handleTestItemChange,
            handleTestLevelChange,
            handleOtherTestChange,
            handleFileUpload,
            updateTestSampleCount,
            handlePackagingMaterialChange,
            submitForm,
            resetForm
        };
    },
    template: `
        <div class="sample-form">
            <style>
                .fade-enter-active, .fade-leave-active {
                    transition: opacity 0.3s ease, transform 0.3s ease;
                }
                .fade-enter-from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                .fade-leave-to {
                    opacity: 0;
                    transform: translateY(-10px);
                }
            </style>
            <form @submit.prevent="submitForm" class="space-y-6">
                <!-- 基本信息区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4 text-center">样品寄送通知单</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">日期</label>
                            <input type="date" v-model="formData.date"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-xs text-gray-500 mt-1">格式: {{ formattedDate }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">编号</label>
                            <input type="text" :value="generatedNumber" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600">
                            <p class="text-xs text-gray-500 mt-1">自动生成</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邮寄目的</label>
                            <input type="text" v-model="formData.purpose"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入邮寄目的">
                        </div>
                    </div>
                </div>

                <!-- 公司信息区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">公司信息</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">公司名称 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="formData.companyName" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入公司名称">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">收件人 <span class="text-red-500">*</span></label>
                            <input type="text" v-model="formData.recipient" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入收件人">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">电话</label>
                            <input type="tel" v-model="formData.application"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   placeholder="请输入联系电话">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">地址</label>
                            <textarea v-model="formData.address" rows="2"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入地址"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 样品内容区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6 text-center">样品内容</h3>

                    <!-- 产品规格和客制化说明区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 产品规格区域（左侧） -->
                        <div class="bg-gray-50 rounded-lg border p-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">产品规格</h4>
                            <div class="space-y-4">
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">型号 <span class="text-red-500">*</span></label>
                                <input type="text" v-model="formData.productModel" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="<型号+产品描述>">
                                </div>
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">产品描述</label>
                                <textarea v-model="formData.productDescription" rows="2"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                          placeholder="请输入产品描述"></textarea>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">外层</label>
                                    <input type="text" v-model="formData.outerLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">中层</label>
                                    <input type="text" v-model="formData.middleLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">内层</label>
                                    <input type="text" v-model="formData.innerLayer"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">头带</label>
                                    <input type="text" v-model="formData.headband"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">鼻夹</label>
                                    <input type="text" v-model="formData.noseBridge"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">鼻垫</label>
                                    <input type="text" v-model="formData.earLoop"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                </div>

                                <!-- 气阀信息 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">气阀</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valve" value="none" class="mr-2">
                                            <span class="text-sm">无</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valve" value="with" class="mr-2">
                                            <span class="text-sm">有</span>
                                        </label>
                                    </div>
                                    <div v-if="formData.valve === 'with'" class="space-y-2 pl-4">
                                        <div>
                                            <input type="text" v-model="formData.valveTopCover" placeholder="气阀上下盖"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <input type="text" v-model="formData.valveSheet" placeholder="气阀片"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <!-- 调整扣和是否内测 -->
                                <div class="border-t pt-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">调整扣</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" v-model="formData.adjustmentBuckle" value="none" class="mr-2">
                                                <span class="text-sm">无</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" v-model="formData.adjustmentBuckle" value="with" class="mr-2">
                                                <span class="text-sm">有</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">是否内测</label>
                                        <div class="flex items-center space-x-4">
                                            <label class="flex items-center">
                                                <input type="radio" :value="'yes'" :checked="formData.internalTest === 'yes'"
                                                       @change="handleInternalTestChange('yes')" class="mr-2">
                                                <span class="text-sm">是</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" :value="'no'" :checked="formData.internalTest === 'no'"
                                                       @change="handleInternalTestChange('no')" class="mr-2">
                                                <span class="text-sm">否</span>
                                            </label>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">选择"是"将显示测试项目填写区域</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- 客制化说明区域（右侧） -->
                        <div class="bg-gray-50 rounded-lg border p-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">客制化说明</h4>
                            <div class="space-y-4">
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">数量 <span class="text-red-500">*</span></label>
                                <input type="number" v-model="formData.quantity" required min="1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                       placeholder="请输入数量">
                                </div>

                                <!-- 口罩印字 -->
                                <div class="border-b pb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">口罩印字</label>
                                <div class="space-y-2">
                                    <input type="text" v-model="formData.maskPrintFile" placeholder="<附件印字图文件>"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <div class="grid grid-cols-2 gap-2">
                                        <input type="text" v-model="formData.printColor" placeholder="印字颜色"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <input type="text" v-model="formData.printSize" placeholder="印字尺寸"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                                </div>

                                <!-- 头带颜色 -->
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">头带颜色</label>
                                <input type="text" v-model="formData.headbandColor"
                                       placeholder="须为工程管理有认证之颜色"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">须为工程管理有认证之颜色</p>
                                </div>

                                <!-- 气阀颜色 -->
                                <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">气阀颜色</label>
                                <input type="text" v-model="formData.valveColor"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                                <!-- 气阀印字 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">气阀印字</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-4">
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valvePrint" value="none" class="mr-2">
                                            <span class="text-sm">无气阀印字</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" v-model="formData.valvePrint" value="with" class="mr-2">
                                            <span class="text-sm">带气阀印字</span>
                                        </label>
                                    </div>
                                    <div v-if="formData.valvePrint === 'with'" class="space-y-2 pl-4">
                                        <div>
                                            <input type="text" v-model="formData.printMaterial" placeholder="印字用料: <附件印字图文件>"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div class="grid grid-cols-2 gap-2">
                                            <input type="text" v-model="formData.printMaterialColor" placeholder="印字颜色"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <input type="text" v-model="formData.printMaterialSize" placeholder="印字尺寸"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                </div>

                                <!-- 包装信息 -->
                                <div class="border-t pt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-3">包装信息</label>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">包装方式</label>
                                        <input type="text" v-model="formData.packagingMethod"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                               placeholder="请输入包装方式">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">包装材质&尺寸（可多选）</label>
                                        <div class="space-y-4">
                                            <!-- 彩盒 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.colorBox" class="mr-2">
                                                    <span class="text-sm">彩盒</span>
                                                </label>
                                                <div v-if="formData.colorBox" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">上传彩盒图档</label>
                                                    <input type="file"
                                                           @change="handleFileUpload('colorBox', $event)"
                                                           accept="image/*,.pdf,.ai,.eps"
                                                           class="text-xs border border-gray-300 rounded px-2 py-1 w-full">
                                                    <p class="text-xs text-gray-500 mt-1">支持图片、PDF、AI、EPS格式</p>
                                                </div>
                                            </div>

                                            <!-- 胶带 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.tape" class="mr-2">
                                                    <span class="text-sm">胶带</span>
                                                </label>
                                                <div v-if="formData.tape" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">胶带材质和尺寸</label>
                                                    <input type="text"
                                                           v-model="formData.tapeMaterial"
                                                           placeholder="请输入胶带材质和尺寸"
                                                           class="text-xs border border-gray-300 rounded px-2 py-1 w-full">
                                                </div>
                                            </div>

                                            <!-- 泡壳 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.blisterPack" class="mr-2">
                                                    <span class="text-sm">泡壳</span>
                                                </label>
                                                <div v-if="formData.blisterPack" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">上传吊卡图档</label>
                                                    <input type="file"
                                                           @change="handleFileUpload('blisterPack', $event)"
                                                           accept="image/*,.pdf,.ai,.eps"
                                                           class="text-xs border border-gray-300 rounded px-2 py-1 w-full">
                                                    <p class="text-xs text-gray-500 mt-1">支持图片、PDF、AI、EPS格式</p>
                                                </div>
                                            </div>

                                            <!-- 其他 -->
                                            <div>
                                                <label class="flex items-center mb-2">
                                                    <input type="checkbox" v-model="formData.other" class="mr-2">
                                                    <span class="text-sm">其他</span>
                                                </label>
                                                <div v-if="formData.other" class="ml-6">
                                                    <label class="block text-xs text-gray-600 mb-1">文字描述</label>
                                                    <textarea v-model="formData.otherDescription"
                                                              rows="2"
                                                              placeholder="请输入其他包装要求的文字描述"
                                                              class="text-xs border border-gray-300 rounded px-2 py-1 w-full"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- 测试项目及数量（可复选）区域 -->
                <transition name="fade" mode="out-in">
                    <div v-if="formData.internalTest === 'yes'" class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">测试项目及数量（可复选）</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">测试项目</label>
                            <div class="space-y-4">
                                <!-- NIOSH美规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('NIOSH美规')"
                                               @change="handleTestItemChange('NIOSH美规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">NIOSH美规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('NIOSH美规')" class="ml-6 mt-2 space-y-2 border-l-2 border-blue-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗')"
                                                       @change="handleTestItemChange('一般穿透阻抗', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗')"
                                                        @change="handleTestLevelChange('一般穿透阻抗', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="N95">N95</option>
                                                    <option value="N99">N99</option>
                                                    <option value="N100">N100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试')"
                                                       @change="handleTestItemChange('加载测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试')"
                                                        @change="handleTestLevelChange('加载测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="N95">N95</option>
                                                    <option value="N99">N99</option>
                                                    <option value="N100">N100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力')"
                                                       @change="handleTestItemChange('头带拉力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力')"
                                                        @change="handleTestLevelChange('头带拉力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力')"
                                                       @change="handleTestItemChange('气阀拉力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力')"
                                                        @change="handleTestLevelChange('气阀拉力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('密合度测试')"
                                                       @change="handleTestItemChange('密合度测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">密合度测试</span>
                                                <select v-if="formData.testItems.includes('密合度测试')"
                                                        @change="handleTestLevelChange('密合度测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="定性">定性</option>
                                                    <option value="定量">定量</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他NIOSH')"
                                                       @change="handleTestItemChange('其他NIOSH', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他NIOSH')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他NIOSH', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- CE欧规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('CE欧规')"
                                               @change="handleTestItemChange('CE欧规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">CE欧规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('CE欧规')" class="ml-6 mt-2 space-y-2 border-l-2 border-green-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗CE')"
                                                       @change="handleTestItemChange('一般穿透阻抗CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗CE')"
                                                        @change="handleTestLevelChange('一般穿透阻抗CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试CE')"
                                                       @change="handleTestItemChange('加载测试CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试CE')"
                                                        @change="handleTestLevelChange('加载测试CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('全方位阻抗CE')"
                                                       @change="handleTestItemChange('全方位阻抗CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">全方位阻抗</span>
                                                <select v-if="formData.testItems.includes('全方位阻抗CE')"
                                                        @change="handleTestLevelChange('全方位阻抗CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('总泄漏率')"
                                                       @change="handleTestItemChange('总泄漏率', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">总泄漏率</span>
                                                <select v-if="formData.testItems.includes('总泄漏率')"
                                                        @change="handleTestLevelChange('总泄漏率', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="FFP1">FFP1</option>
                                                    <option value="FFP2">FFP2</option>
                                                    <option value="FFP3">FFP3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力CE')"
                                                       @change="handleTestItemChange('头带拉力CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力CE')"
                                                        @change="handleTestLevelChange('头带拉力CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力CE')"
                                                       @change="handleTestItemChange('气阀拉力CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力CE')"
                                                        @change="handleTestLevelChange('气阀拉力CE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('白云石')"
                                                       @change="handleTestItemChange('白云石', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">白云石</span>
                                                <select v-if="formData.testItems.includes('白云石')"
                                                        @change="handleTestLevelChange('白云石', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他CE')"
                                                       @change="handleTestItemChange('其他CE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他CE')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他CE', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- AS/NZS澳规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('AS/NZS澳规')"
                                               @change="handleTestItemChange('AS/NZS澳规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">AS/NZS澳规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('AS/NZS澳规')" class="ml-6 mt-2 space-y-2 border-l-2 border-yellow-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗澳规')"
                                                       @change="handleTestItemChange('一般穿透阻抗澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗澳规')"
                                                        @change="handleTestLevelChange('一般穿透阻抗澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="P1">P1</option>
                                                    <option value="P2">P2</option>
                                                    <option value="P3">P3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('全方位阻抗澳规')"
                                                       @change="handleTestItemChange('全方位阻抗澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">全方位阻抗</span>
                                                <select v-if="formData.testItems.includes('全方位阻抗澳规')"
                                                        @change="handleTestLevelChange('全方位阻抗澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="P1">P1</option>
                                                    <option value="P2">P2</option>
                                                    <option value="P3">P3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力澳规')"
                                                       @change="handleTestItemChange('头带拉力澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力澳规')"
                                                        @change="handleTestLevelChange('头带拉力澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力澳规')"
                                                       @change="handleTestItemChange('气阀拉力澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力澳规')"
                                                        @change="handleTestLevelChange('气阀拉力澳规', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他澳规')"
                                                       @change="handleTestItemChange('其他澳规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他澳规')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他澳规', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- 日规 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('日规')"
                                               @change="handleTestItemChange('日规', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">日规</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('日规')" class="ml-6 mt-2 space-y-2 border-l-2 border-red-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('粉尘和吸气上升值')"
                                                       @change="handleTestItemChange('粉尘和吸气上升值', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">粉尘和吸气上升值</span>
                                                <select v-if="formData.testItems.includes('粉尘和吸气上升值')"
                                                        @change="handleTestLevelChange('粉尘和吸气上升值', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="DS1">DS1</option>
                                                    <option value="DS2">DS2</option>
                                                    <option value="DS3">DS3</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('湿阻力')"
                                                       @change="handleTestItemChange('湿阻力', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">湿阻力</span>
                                                <select v-if="formData.testItems.includes('湿阻力')"
                                                        @change="handleTestLevelChange('湿阻力', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('使用时间')"
                                                       @change="handleTestItemChange('使用时间', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">使用时间</span>
                                                <select v-if="formData.testItems.includes('使用时间')"
                                                        @change="handleTestLevelChange('使用时间', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="8小时">8小时</option>
                                                    <option value="12小时">12小时</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('吸气排气阻力测试')"
                                                       @change="handleTestItemChange('吸气排气阻力测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">吸气排气阻力测试</span>
                                                <select v-if="formData.testItems.includes('吸气排气阻力测试')"
                                                        @change="handleTestLevelChange('吸气排气阻力测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('泄漏率')"
                                                       @change="handleTestItemChange('泄漏率', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">泄漏率</span>
                                                <select v-if="formData.testItems.includes('泄漏率')"
                                                        @change="handleTestLevelChange('泄漏率', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="严格">严格</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他日规')"
                                                       @change="handleTestItemChange('其他日规', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他日规')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他日规', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- GB国标 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('GB国标')"
                                               @change="handleTestItemChange('GB国标', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">GB国标</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('GB国标')" class="ml-6 mt-2 space-y-2 border-l-2 border-purple-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('一般穿透阻抗GB')"
                                                       @change="handleTestItemChange('一般穿透阻抗GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">一般穿透阻抗</span>
                                                <select v-if="formData.testItems.includes('一般穿透阻抗GB')"
                                                        @change="handleTestLevelChange('一般穿透阻抗GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="KN90">KN90</option>
                                                    <option value="KN95">KN95</option>
                                                    <option value="KN100">KN100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('加载测试GB')"
                                                       @change="handleTestItemChange('加载测试GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">加载测试</span>
                                                <select v-if="formData.testItems.includes('加载测试GB')"
                                                        @change="handleTestLevelChange('加载测试GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="KN90">KN90</option>
                                                    <option value="KN95">KN95</option>
                                                    <option value="KN100">KN100</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('头带拉力GB')"
                                                       @change="handleTestItemChange('头带拉力GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">头带拉力</span>
                                                <select v-if="formData.testItems.includes('头带拉力GB')"
                                                        @change="handleTestLevelChange('头带拉力GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('气阀拉力GB')"
                                                       @change="handleTestItemChange('气阀拉力GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">气阀拉力</span>
                                                <select v-if="formData.testItems.includes('气阀拉力GB')"
                                                        @change="handleTestLevelChange('气阀拉力GB', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('泄漏测试')"
                                                       @change="handleTestItemChange('泄漏测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">泄漏测试</span>
                                                <select v-if="formData.testItems.includes('泄漏测试')"
                                                        @change="handleTestLevelChange('泄漏测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="严格">严格</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他GB')"
                                                       @change="handleTestItemChange('其他GB', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他GB')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他GB', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>

                                <!-- 医疗项目 -->
                                <div>
                                    <label class="flex items-center">
                                        <input type="checkbox"
                                               :checked="formData.testItems.includes('医疗项目')"
                                               @change="handleTestItemChange('医疗项目', $event.target.checked)"
                                               class="mr-2">
                                        <span class="text-sm font-medium">医疗项目</span>
                                    </label>
                                    <transition name="fade">
                                        <div v-if="formData.testItems.includes('医疗项目')" class="ml-6 mt-2 space-y-2 border-l-2 border-pink-200 pl-4">
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('血透测试')"
                                                       @change="handleTestItemChange('血透测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">血透测试</span>
                                                <select v-if="formData.testItems.includes('血透测试')"
                                                        @change="handleTestLevelChange('血透测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="标准">标准</option>
                                                    <option value="加强">加强</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('BFE')"
                                                       @change="handleTestItemChange('BFE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">BFE</span>
                                                <select v-if="formData.testItems.includes('BFE')"
                                                        @change="handleTestLevelChange('BFE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≥95%">≥95%</option>
                                                    <option value="≥98%">≥98%</option>
                                                    <option value="≥99%">≥99%</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('PFE')"
                                                       @change="handleTestItemChange('PFE', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">PFE</span>
                                                <select v-if="formData.testItems.includes('PFE')"
                                                        @change="handleTestLevelChange('PFE', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≥95%">≥95%</option>
                                                    <option value="≥98%">≥98%</option>
                                                    <option value="≥99%">≥99%</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('压差')"
                                                       @change="handleTestItemChange('压差', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">压差</span>
                                                <select v-if="formData.testItems.includes('压差')"
                                                        @change="handleTestLevelChange('压差', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="≤49Pa">≤49Pa</option>
                                                    <option value="≤60Pa">≤60Pa</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('阻燃测试')"
                                                       @change="handleTestItemChange('阻燃测试', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">阻燃测试</span>
                                                <select v-if="formData.testItems.includes('阻燃测试')"
                                                        @change="handleTestLevelChange('阻燃测试', $event.target.value)"
                                                        class="text-xs border border-gray-300 rounded px-2 py-1">
                                                    <option value="">选择等级</option>
                                                    <option value="Class 1">Class 1</option>
                                                    <option value="Class 2">Class 2</option>
                                                </select>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <input type="checkbox"
                                                       :checked="formData.testItems.includes('其他医疗')"
                                                       @change="handleTestItemChange('其他医疗', $event.target.checked)"
                                                       class="mr-2">
                                                <span class="text-sm text-gray-600">其他：</span>
                                                <input v-if="formData.testItems.includes('其他医疗')"
                                                       type="text"
                                                       @input="handleOtherTestChange('其他医疗', $event.target.value)"
                                                       placeholder="请输入其他项目"
                                                       class="text-xs border border-gray-300 rounded px-2 py-1 flex-1">
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">样品测试数量</label>
                            <div v-if="Object.keys(groupedTestCounts).length === 0" class="text-sm text-gray-500 italic">
                                请先选择测试项目
                            </div>
                            <div v-else class="space-y-4 max-h-80 overflow-y-auto">
                                <div v-for="(items, category) in groupedTestCounts" :key="category"
                                     class="border border-gray-200 rounded-lg p-3">
                                    <h4 class="text-sm font-medium text-gray-800 mb-2 border-b border-gray-100 pb-1">
                                        {{ category }}
                                    </h4>
                                    <div class="space-y-2">
                                        <div v-for="item in items" :key="item.name"
                                             class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                            <span class="text-xs text-gray-700 flex-1">{{ item.name }}</span>
                                            <div class="flex items-center space-x-2">
                                                <input type="number"
                                                       :value="item.count"
                                                       @input="updateTestSampleCount(item.name, $event.target.value)"
                                                       min="1"
                                                       max="100"
                                                       class="w-14 text-xs border border-gray-300 rounded px-1 py-1 text-center">
                                                <span class="text-xs text-gray-500">个</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-500 mt-2">数量会根据测试项目自动设置，您可以手动调整</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">测试备注</label>
                            <textarea v-model="formData.testNotes" rows="6"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="请输入测试备注"></textarea>
                        </div>
                    </div>
                    </div>
                </transition>

                <!-- 收件人信息区域 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">收件人信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">To: <span class="text-red-500">*</span></label>
                            <select v-model="formData.to" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择收件人</option>
                                <option v-if="loadingUsers" disabled>加载用户中...</option>
                                <option v-for="user in users" :key="user.id" :value="user.username">
                                    {{ user.username }} ({{ user.email }})
                                </option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Cc:</label>
                            <select v-model="formData.cc"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择抄送人</option>
                                <option v-if="loadingUsers" disabled>加载用户中...</option>
                                <option v-for="user in users" :key="user.id" :value="user.username">
                                    {{ user.username }} ({{ user.email }})
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="bg-white rounded-lg shadow border p-6">
                    <div class="flex justify-end space-x-4">
                        <button type="button" @click="resetForm"
                                class="px-6 py-3 bg-gray-600 text-white font-medium rounded-lg hover:bg-gray-700 transition-colors">
                            重置表单
                        </button>
                        <button type="submit" :disabled="submitting"
                                class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <span v-if="submitting">提交中...</span>
                            <span v-else>提交样品单</span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    `
};
