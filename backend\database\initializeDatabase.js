/**
 * 数据库自动初始化脚本 - 根据当前PostgreSQL数据库结构生成
 * 在新服务器环境中自动创建所有必需的数据库表
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');
require('dotenv').config();

class DatabaseInitializer {
    constructor() {
        this.pool = new Pool({
            host: process.env.POSTGRES_HOST || 'localhost',
            port: process.env.POSTGRES_PORT || 5432,
            user: process.env.POSTGRES_USER || 'postgres',
            password: process.env.POSTGRES_PASSWORD,
            database: process.env.POSTGRES_DATABASE || 'makrite_system',
            max: parseInt(process.env.POSTGRES_POOL_MAX) || 20,
            min: parseInt(process.env.POSTGRES_POOL_MIN) || 2,
            idleTimeoutMillis: parseInt(process.env.POSTGRES_POOL_IDLE_TIMEOUT) || 30000,
            connectionTimeoutMillis: parseInt(process.env.POSTGRES_POOL_CONNECTION_TIMEOUT) || 2000,
        });
    }

    /**
     * 初始化所有数据库表
     */
    async initializeAllTables() {
        try {
            logger.info('开始数据库表初始化...');

            // 1. 检查数据库连接
            await this.testConnection();

            // 2. 检查数据库环境
            await this.checkDatabaseEnvironment();

            // 3. 创建所有表
            await this.createUsersTable();
            await this.createApplicationsTable();
            await this.createApplicationApprovalsTable();
            await this.createApplicationAttachmentsTable();
            await this.createApplicationDraftsTable();
            await this.createApprovalHistoryTable();
            await this.createAuditLogsTable();
            await this.createBackupRecordsTable();
            await this.createCapacityTable();
            await this.createCustomerFilesTable();
            await this.createCustomerFileAttachmentsTable();
            await this.createCustomerFileConfirmationsTable();
            await this.createCustomerFileNotificationsTable();
            await this.createCustomerFileNumberReservationsTable();
            await this.createDepartmentsTable();
            await this.createEquipmentTable();
            await this.createEquipmentCapabilitiesTable();
            await this.createEquipmentHealthTable();
            await this.createEquipmentHealthHistoryTable();
            await this.createEquipmentMaintenanceTable();
            await this.createFactoriesTable();
            await this.createFileManagementTable();
            await this.createFileManagementFilesTable();
            await this.createFileManagementAttachmentsTable();
            await this.createFileManagementCustomersTable();
            await this.createFileManagementNotificationsTable();
            await this.createFileManagementNumberReservationsTable();
            await this.createFileManagementProductsTable();
            await this.createLoginAttemptsTable();
            await this.createMaintenanceTable();
            await this.createMigrationsTable();
            await this.createOperatorSkillsTable();
            await this.createPasswordResetTokensTable();
            await this.createPerformanceLogsTable();
            await this.createPermissionTemplatesTable();
            await this.createProductsTable();
            await this.createProductionProcessesTable();
            await this.createQualityTable();
            await this.createQualityReportsTable();
            await this.createQualityReportFilesTable();
            await this.createQualityReportNumberReservationsTable();
            await this.createQualityReportNumbersTable();
            await this.createScheduleTable();
            await this.createSchedulesTable();
            await this.createSchedulePlansTable();
            await this.createScheduleProgressTable();
            await this.createSystemConfigTable();
            await this.createSystemLogsTable();
            await this.createSystemMaintenanceTable();
            await this.createUserActivityLogsTable();
            await this.createUserAvatarsTable();
            await this.createUserLoginLogsTable();
            await this.createUserNotificationsTable();
            await this.createUserPreferencesTable();
            await this.createUserProfilesTable();
            await this.createUserSessionsTable();
            await this.createUserSignaturesTable();
            await this.createUserTrackingTable();
            await this.createWarehouseFinishedProductsTable();
            await this.createWarehouseInventoryTransactionsTable();
            await this.createWarehouseMaterialsTable();
            await this.createWarehouseProductsTable();
            await this.createWarehouseQrcodesTable();
            await this.createWarehouseTransactionsTable();

            // 4. 验证表结构完整性
            await this.validateTableStructure();

            // 5. 执行数据库迁移
            await this.runMigrations();

            logger.info('数据库表初始化完成！');
            return true;
        } catch (error) {
            logger.error('数据库表初始化失败:', error);
            throw error;
        }
    }

    /**
     * 测试数据库连接
     */
    async testConnection() {
        try {
            const client = await this.pool.connect();
            await client.query('SELECT NOW()');
            client.release();
            logger.info('数据库连接测试成功');
        } catch (error) {
            logger.error('数据库连接失败:', error);
            throw new Error('无法连接到PostgreSQL数据库');
        }
    }

    /**
     * 检查数据库环境
     */
    async checkDatabaseEnvironment() {
        try {
            const client = await this.pool.connect();

            // 检查PostgreSQL版本
            const versionResult = await client.query('SELECT version()');
            const version = versionResult.rows[0].version;
            logger.info(`PostgreSQL版本: ${version}`);

            // 检查数据库名称
            const dbResult = await client.query('SELECT current_database()');
            const dbName = dbResult.rows[0].current_database;
            logger.info(`当前数据库: ${dbName}`);

            // 检查用户权限
            const userResult = await client.query('SELECT current_user, session_user');
            const currentUser = userResult.rows[0].current_user;
            logger.info(`数据库用户: ${currentUser}`);

            // 检查必要的扩展
            await this.checkRequiredExtensions(client);

            // 检查字符编码
            const encodingResult = await client.query('SHOW server_encoding');
            const encoding = encodingResult.rows[0].server_encoding;
            logger.info(`数据库编码: ${encoding}`);

            client.release();
            logger.info('数据库环境检查完成');
        } catch (error) {
            logger.error('数据库环境检查失败:', error);
            throw error;
        }
    }

    /**
     * 检查必要的扩展
     */
    async checkRequiredExtensions(client) {
        try {
            // 启用UUID扩展
            await client.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
            logger.info('UUID扩展已启用');

            // 检查扩展是否可用
            const extensionResult = await client.query(`
                SELECT extname FROM pg_extension WHERE extname = 'uuid-ossp'
            `);

            if (extensionResult.rows.length === 0) {
                throw new Error('UUID扩展未能正确安装');
            }
        } catch (error) {
            logger.error('检查数据库扩展失败:', error);
            throw error;
        }
    }

    /**
     * 创建application_approvals表
     */
    async createApplicationApprovalsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS application_approvals (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    approver_id VARCHAR(50) NOT NULL,
    approver_name VARCHAR(100) NOT NULL,
    stage VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    comments TEXT,
    approved_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (application_id) REFERENCES applications (id),
    FOREIGN KEY (approver_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('application_approvals', sql);
    }

    /**
     * 创建application_attachments表
     */
    async createApplicationAttachmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS application_attachments (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    path TEXT NOT NULL,
    filename VARCHAR(255),
    type VARCHAR(100),
    size INTEGER,
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('application_attachments', sql);
    }

    /**
     * 创建application_drafts表
     */
    async createApplicationDraftsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS application_drafts (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    type VARCHAR(50),
    draft_data TEXT NOT NULL,
    auto_saved BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('application_drafts', sql);
    }

    /**
     * 创建applications表
     */
    async createApplicationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS applications (
    id VARCHAR(50) PRIMARY KEY,
    application_number VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    applicant VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    date DATE NOT NULL,
    content TEXT NOT NULL,
    amount VARCHAR(100),
    priority VARCHAR(20) DEFAULT 'normal'::character varying,
    type VARCHAR(50) DEFAULT 'standard'::character varying,
    status VARCHAR(20) DEFAULT 'pending'::character varying,
    current_stage VARCHAR(50),
    need_manager_approval BOOLEAN DEFAULT false,
    need_ceo_approval BOOLEAN DEFAULT true,
    selected_factory_managers JSONB DEFAULT '[]',
    selected_managers JSONB DEFAULT '[]',
    pdf_path VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('applications', sql);
    }

    /**
     * 创建approval_history表
     */
    async createApprovalHistoryTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS approval_history (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    stage VARCHAR(50) NOT NULL,
    approver_id VARCHAR(50) NOT NULL,
    approver_name VARCHAR(100) NOT NULL,
    approver_role VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL,
    comment TEXT,
    signature_path TEXT,
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('approval_history', sql);
    }

    /**
     * 创建audit_logs表
     */
    async createAuditLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS audit_logs (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255),
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('audit_logs', sql);
    }

    /**
     * 创建backup_records表
     */
    async createBackupRecordsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS backup_records (
    id VARCHAR(255) PRIMARY KEY,
    backup_name VARCHAR(255) NOT NULL,
    backup_type VARCHAR(50),
    file_path VARCHAR(500),
    file_size BIGINT,
    status VARCHAR(50) DEFAULT 'pending'::character varying,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
        `;
        await this.executeSQL('backup_records', sql);
    }

    /**
     * 创建capacity表
     */
    async createCapacityTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS capacity (
    id VARCHAR(50) PRIMARY KEY,
    date DATE UNIQUE NOT NULL,
    department_id VARCHAR(50) UNIQUE NOT NULL,
    shift VARCHAR(20) UNIQUE NOT NULL,
    available_hours DECIMAL(12,2) NOT NULL DEFAULT 0,
    planned_hours DECIMAL(12,2) NOT NULL DEFAULT 0,
    actual_hours DECIMAL(12,2) DEFAULT 0,
    efficiency_rate DECIMAL(12,2) DEFAULT 0,
    utilization_rate DECIMAL(12,2) DEFAULT 0,
    worker_count INTEGER DEFAULT 0,
    equipment_count INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('capacity', sql);
    }

    /**
     * 创建customer_file_attachments表
     */
    async createCustomerFileAttachmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS customer_file_attachments (
    id VARCHAR(255) PRIMARY KEY,
    customer_file_id VARCHAR(255),
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP DEFAULT NOW(),
    file_id TEXT,
    original_filename TEXT,
    stored_filename TEXT,
    file_type TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id)
);
        `;
        await this.executeSQL('customer_file_attachments', sql);
    }

    /**
     * 创建customer_file_confirmations表
     */
    async createCustomerFileConfirmationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS customer_file_confirmations (
    id VARCHAR(255) PRIMARY KEY,
    customer_file_id VARCHAR(255),
    confirmed_by VARCHAR(255),
    confirmation_status VARCHAR(50),
    notes TEXT,
    confirmed_at TIMESTAMP DEFAULT NOW(),
    file_id TEXT,
    user_id TEXT,
    downloaded_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id)
);
        `;
        await this.executeSQL('customer_file_confirmations', sql);
    }

    /**
     * 创建customer_file_notifications表
     */
    async createCustomerFileNotificationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS customer_file_notifications (
    id VARCHAR(255) PRIMARY KEY,
    customer_file_id VARCHAR(255),
    recipient VARCHAR(255) NOT NULL,
    message TEXT,
    notification_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending'::character varying,
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    file_id TEXT,
    user_id TEXT,
    notified_at TIMESTAMP,
    email_sent BOOLEAN DEFAULT false,
    confirmed BOOLEAN DEFAULT false,
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (customer_file_id) REFERENCES customer_files (id)
);
        `;
        await this.executeSQL('customer_file_notifications', sql);
    }

    /**
     * 创建customer_file_number_reservations表
     */
    async createCustomerFileNumberReservationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS customer_file_number_reservations (
    id VARCHAR(255) PRIMARY KEY,
    file_number VARCHAR(255) NOT NULL,
    customer_id VARCHAR(255),
    reserved_by VARCHAR(255),
    reserved_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('customer_file_number_reservations', sql);
    }

    /**
     * 创建customer_files表
     */
    async createCustomerFilesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS customer_files (
    id VARCHAR(255) PRIMARY KEY,
    customer_id VARCHAR(255) NOT NULL,
    file_number VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    file_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'active'::character varying,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    customer_name TEXT,
    model TEXT,
    batch TEXT,
    version TEXT,
    content_type TEXT,
    initial_content TEXT,
    change_content TEXT,
    uploaded_by TEXT,
    uploaded_at TIMESTAMP
);
        `;
        await this.executeSQL('customer_files', sql);
    }

    /**
     * 创建departments表
     */
    async createDepartmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS departments (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    sort_order INTEGER DEFAULT 0
);
        `;
        await this.executeSQL('departments', sql);
    }

    /**
     * 创建equipment表
     */
    async createEquipmentTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    area VARCHAR(100),
    location VARCHAR(200),
    responsible VARCHAR(100),
    manufacture_date DATE,
    status VARCHAR(20) DEFAULT 'active'::character varying,
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('equipment', sql);
    }

    /**
     * 创建equipment_capabilities表
     */
    async createEquipmentCapabilitiesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_capabilities (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    product_type VARCHAR(100),
    capacity_per_hour DECIMAL(12,2),
    efficiency_rate DECIMAL(12,2) DEFAULT 100.00,
    setup_time INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);
        `;
        await this.executeSQL('equipment_capabilities', sql);
    }

    /**
     * 创建equipment_health表
     */
    async createEquipmentHealthTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_health (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    health_score DECIMAL(12,2) NOT NULL,
    temperature DECIMAL(12,2),
    vibration DECIMAL(12,2),
    pressure DECIMAL(12,2),
    runtime_hours DECIMAL(12,2),
    maintenance_status VARCHAR(50),
    last_maintenance DATE,
    next_maintenance DATE,
    alerts JSONB DEFAULT '[]',
    recorded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    age_score DECIMAL(12,2),
    repair_frequency_score DECIMAL(12,2),
    fault_severity_score DECIMAL(12,2),
    maintenance_score DECIMAL(12,2),
    assessment_date TIMESTAMP,
    assessor VARCHAR(100),
    total_score INTEGER,
    health_level VARCHAR(20),
    calculation_details TEXT,
    recommendations TEXT,
    next_maintenance_date DATE,
    failure_probability DECIMAL(5,4),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);
        `;
        await this.executeSQL('equipment_health', sql);
    }

    /**
     * 创建equipment_health_history表
     */
    async createEquipmentHealthHistoryTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_health_history (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    total_score DECIMAL(12,2) NOT NULL,
    health_level VARCHAR(20) NOT NULL,
    age_score DECIMAL(12,2),
    repair_frequency_score DECIMAL(12,2),
    fault_severity_score DECIMAL(12,2),
    maintenance_score DECIMAL(12,2),
    assessment_date TIMESTAMP NOT NULL,
    assessor VARCHAR(100) NOT NULL,
    calculation_details JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    next_maintenance_date DATE,
    failure_probability DECIMAL(12,2) DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);
        `;
        await this.executeSQL('equipment_health_history', sql);
    }

    /**
     * 创建equipment_maintenance表
     */
    async createEquipmentMaintenanceTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS equipment_maintenance (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    maintenance_type VARCHAR(50) NOT NULL,
    maintenance_date DATE NOT NULL,
    description TEXT,
    cost DECIMAL(12,2),
    technician VARCHAR(100),
    severity_level VARCHAR(20) DEFAULT 'moderate'::character varying,
    status VARCHAR(20) DEFAULT 'completed'::character varying,
    duration_hours DECIMAL(12,2),
    parts_used JSONB DEFAULT '[]',
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    start_time TIME WITHOUT TIME ZONE,
    end_time TIME WITHOUT TIME ZONE,
    result TEXT,
    reviewer VARCHAR(100),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);
        `;
        await this.executeSQL('equipment_maintenance', sql);
    }

    /**
     * 创建factories表
     */
    async createFactoriesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS factories (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    address TEXT,
    manager VARCHAR(100),
    contact_phone VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active'::character varying,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('factories', sql);
    }

    /**
     * 创建file_management表
     */
    async createFileManagementTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management (
    id VARCHAR(50) PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    mime_type VARCHAR(100),
    category VARCHAR(100) NOT NULL,
    description TEXT,
    tags JSONB DEFAULT '[]',
    uploaded_by VARCHAR(50) NOT NULL,
    department_id VARCHAR(50),
    is_public BOOLEAN DEFAULT false,
    download_count INTEGER DEFAULT 0,
    version VARCHAR(20) DEFAULT '1.0'::character varying,
    status VARCHAR(20) DEFAULT 'active'::character varying,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('file_management', sql);
    }

    /**
     * 创建file_management_attachments表
     */
    async createFileManagementAttachmentsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_attachments (
    id VARCHAR(255) PRIMARY KEY,
    file_id VARCHAR(255),
    filename VARCHAR(255),
    original_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_by VARCHAR(255),
    uploaded_at TIMESTAMP DEFAULT NOW(),
    file_record_id VARCHAR(255),
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_type VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (file_id) REFERENCES file_management_files (id)
);
        `;
        await this.executeSQL('file_management_attachments', sql);
    }

    /**
     * 创建file_management_customers表
     */
    async createFileManagementCustomersTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_customers (
    id VARCHAR(50) PRIMARY KEY,
    customer_name VARCHAR(200) NOT NULL,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(255),
    address TEXT,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    customer_code VARCHAR(255),
    description TEXT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255)
);
        `;
        await this.executeSQL('file_management_customers', sql);
    }

    /**
     * 创建file_management_files表
     */
    async createFileManagementFilesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_files (
    id VARCHAR(255) PRIMARY KEY,
    file_number VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    customer_id VARCHAR(255),
    product_id VARCHAR(255),
    file_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'draft'::character varying,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    version INTEGER DEFAULT 1,
    is_first_version BOOLEAN DEFAULT true,
    change_description TEXT,
    uploaded_by VARCHAR(255),
    uploaded_at TIMESTAMP
);
        `;
        await this.executeSQL('file_management_files', sql);
    }

    /**
     * 创建file_management_notifications表
     */
    async createFileManagementNotificationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_notifications (
    id VARCHAR(255) PRIMARY KEY,
    file_id VARCHAR(255),
    recipient VARCHAR(255) NOT NULL,
    message TEXT,
    notification_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending'::character varying,
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    file_record_id TEXT,
    notified_user_id TEXT,
    confirmed_at TIMESTAMP,
    confirmed BOOLEAN DEFAULT false,
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (file_id) REFERENCES file_management_files (id)
);
        `;
        await this.executeSQL('file_management_notifications', sql);
    }

    /**
     * 创建file_management_number_reservations表
     */
    async createFileManagementNumberReservationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_number_reservations (
    id VARCHAR(255) PRIMARY KEY,
    file_number VARCHAR(255) NOT NULL,
    reserved_by VARCHAR(255),
    reserved_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('file_management_number_reservations', sql);
    }

    /**
     * 创建file_management_products表
     */
    async createFileManagementProductsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS file_management_products (
    id VARCHAR(255) PRIMARY KEY,
    product_code VARCHAR(255) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    specifications TEXT,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    customer_id VARCHAR(255),
    product_model VARCHAR(255),
    batch_number VARCHAR(255),
    specification TEXT,
    active BOOLEAN DEFAULT true
);
        `;
        await this.executeSQL('file_management_products', sql);
    }

    /**
     * 创建login_attempts表
     */
    async createLoginAttemptsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS login_attempts (
    id VARCHAR(255) PRIMARY KEY,
    username VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    success BOOLEAN DEFAULT false,
    failure_reason VARCHAR(255),
    attempted_at TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('login_attempts', sql);
    }

    /**
     * 创建maintenance表
     */
    async createMaintenanceTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS maintenance (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    maintenance_type VARCHAR(50) NOT NULL,
    scheduled_date DATE NOT NULL,
    actual_date DATE,
    duration_hours DECIMAL(12,2),
    cost DECIMAL(12,2),
    technician VARCHAR(100),
    description TEXT,
    parts_used JSONB DEFAULT '[]',
    status VARCHAR(20) NOT NULL DEFAULT 'scheduled'::character varying,
    priority VARCHAR(20) DEFAULT 'normal'::character varying,
    notes TEXT,
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (equipment_id) REFERENCES equipment (id)
);
        `;
        await this.executeSQL('maintenance', sql);
    }

    /**
     * 创建migrations表
     */
    async createMigrationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS migrations (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    executed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    success BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('migrations', sql);
    }

    /**
     * 创建operator_skills表
     */
    async createOperatorSkillsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS operator_skills (
    id VARCHAR(50) PRIMARY KEY,
    operator_id VARCHAR(50) NOT NULL,
    equipment_type VARCHAR(100) NOT NULL,
    skill_level INTEGER DEFAULT 1,
    certified BOOLEAN DEFAULT false,
    certification_date DATE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('operator_skills', sql);
    }

    /**
     * 创建password_reset_tokens表
     */
    async createPasswordResetTokensTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    token VARCHAR(500) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT false,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('password_reset_tokens', sql);
    }

    /**
     * 创建performance_logs表
     */
    async createPerformanceLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS performance_logs (
    id VARCHAR(50) PRIMARY KEY,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    response_time INTEGER NOT NULL,
    status_code INTEGER NOT NULL,
    user_id VARCHAR(50),
    ip_address INET,
    error_message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('performance_logs', sql);
    }

    /**
     * 创建permission_templates表
     */
    async createPermissionTemplatesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS permission_templates (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]',
    is_built_in BOOLEAN DEFAULT false,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('permission_templates', sql);
    }

    /**
     * 创建production_processes表
     */
    async createProductionProcessesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS production_processes (
    id VARCHAR(50) PRIMARY KEY,
    product_id VARCHAR(50) NOT NULL,
    step_number INTEGER NOT NULL,
    step_name VARCHAR(200) NOT NULL,
    equipment_type VARCHAR(100),
    standard_time DECIMAL(12,2),
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (product_id) REFERENCES products (id)
);
        `;
        await this.executeSQL('production_processes', sql);
    }

    /**
     * 创建products表
     */
    async createProductsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS products (
    id VARCHAR(50) PRIMARY KEY,
    code VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100),
    specifications JSONB DEFAULT '{}',
    unit VARCHAR(20) DEFAULT 'pcs'::character varying,
    standard_time DECIMAL(12,2),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('products', sql);
    }

    /**
     * 创建quality表
     */
    async createQualityTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality (
    id VARCHAR(50) PRIMARY KEY,
    schedule_id VARCHAR(50) NOT NULL,
    product_id VARCHAR(50) NOT NULL,
    inspector VARCHAR(100) NOT NULL,
    inspection_date DATE NOT NULL,
    batch_number VARCHAR(100),
    sample_size INTEGER NOT NULL,
    passed_count INTEGER NOT NULL DEFAULT 0,
    failed_count INTEGER NOT NULL DEFAULT 0,
    defect_types JSONB DEFAULT '[]',
    quality_score DECIMAL(12,2),
    status VARCHAR(20) NOT NULL DEFAULT 'pending'::character varying,
    notes TEXT,
    attachments JSONB DEFAULT '[]',
    corrective_actions JSONB DEFAULT '[]',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('quality', sql);
    }

    /**
     * 创建quality_report_files表
     */
    async createQualityReportFilesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality_report_files (
    id VARCHAR(50) PRIMARY KEY,
    report_id VARCHAR(50) NOT NULL,
    filename VARCHAR(255),
    original_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    uploaded_at TIMESTAMP NOT NULL DEFAULT NOW(),
    original_filename VARCHAR(255),
    stored_filename VARCHAR(255),
    file_type VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (report_id) REFERENCES quality_reports (id)
);
        `;
        await this.executeSQL('quality_report_files', sql);
    }

    /**
     * 创建quality_report_number_reservations表
     */
    async createQualityReportNumberReservationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality_report_number_reservations (
    id VARCHAR(255) PRIMARY KEY,
    report_number VARCHAR(255) NOT NULL,
    reserved_by VARCHAR(255),
    reserved_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('quality_report_number_reservations', sql);
    }

    /**
     * 创建quality_report_numbers表
     */
    async createQualityReportNumbersTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality_report_numbers (
    report_number VARCHAR(255) PRIMARY KEY,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('quality_report_numbers', sql);
    }

    /**
     * 创建quality_reports表
     */
    async createQualityReportsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS quality_reports (
    id VARCHAR(50) PRIMARY KEY,
    report_number VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    product_batch VARCHAR(100),
    test_date DATE NOT NULL,
    inspector VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending'::character varying,
    summary TEXT,
    uploaded_by VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    description TEXT,
    test_type VARCHAR(100),
    sample_info TEXT,
    test_method TEXT,
    test_standard TEXT,
    test_result TEXT,
    conclusion TEXT,
    uploaded_at TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);
        `;
        await this.executeSQL('quality_reports', sql);
    }

    /**
     * 创建schedule表
     */
    async createScheduleTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS schedule (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    product_id VARCHAR(50) NOT NULL,
    department_id VARCHAR(50) NOT NULL,
    planned_start_date DATE NOT NULL,
    planned_end_date DATE NOT NULL,
    actual_start_date DATE,
    actual_end_date DATE,
    planned_quantity INTEGER NOT NULL,
    actual_quantity INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 5,
    status VARCHAR(20) NOT NULL DEFAULT 'planned'::character varying,
    assigned_workers JSONB DEFAULT '[]',
    assigned_equipment JSONB DEFAULT '[]',
    progress_percentage DECIMAL(12,2) DEFAULT 0,
    quality_status VARCHAR(20) DEFAULT 'pending'::character varying,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('schedule', sql);
    }

    /**
     * 创建schedule_plans表
     */
    async createSchedulePlansTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS schedule_plans (
    id VARCHAR(255) PRIMARY KEY,
    schedule_id VARCHAR(255),
    plan_name VARCHAR(255) NOT NULL,
    description TEXT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    order_id TEXT,
    plan_type TEXT,
    total_time DECIMAL(12,2),
    confidence_score DECIMAL(12,2),
    risk_level TEXT,
    equipment_allocation TEXT,
    operator_allocation TEXT,
    delivery_prediction TEXT,
    optimization_strategy TEXT,
    FOREIGN KEY (schedule_id) REFERENCES schedules (id)
);
        `;
        await this.executeSQL('schedule_plans', sql);
    }

    /**
     * 创建schedule_progress表
     */
    async createScheduleProgressTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS schedule_progress (
    id VARCHAR(255) PRIMARY KEY,
    schedule_id VARCHAR(255),
    progress_percentage INTEGER DEFAULT 0,
    current_stage VARCHAR(255),
    notes TEXT,
    updated_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    progress INTEGER,
    status TEXT,
    FOREIGN KEY (schedule_id) REFERENCES schedules (id)
);
        `;
        await this.executeSQL('schedule_progress', sql);
    }

    /**
     * 创建schedules表
     */
    async createSchedulesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS schedules (
    id VARCHAR(50) PRIMARY KEY,
    schedule_number VARCHAR(100) UNIQUE NOT NULL,
    product_id VARCHAR(50),
    product_name VARCHAR(200) NOT NULL,
    quantity INTEGER NOT NULL,
    unit VARCHAR(20) DEFAULT 'pcs'::character varying,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal'::character varying,
    status VARCHAR(20) DEFAULT 'planned'::character varying,
    equipment_id VARCHAR(50),
    operator_id VARCHAR(50),
    notes TEXT,
    created_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    title VARCHAR(255),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    assigned_equipment TEXT,
    assigned_personnel TEXT,
    required_materials TEXT,
    progress INTEGER DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES users (id)
);
        `;
        await this.executeSQL('schedules', sql);
    }

    /**
     * 创建system_config表
     */
    async createSystemConfigTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS system_config (
    id VARCHAR(255) PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    config_type VARCHAR(50) DEFAULT 'string'::character varying,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
        `;
        await this.executeSQL('system_config', sql);
    }

    /**
     * 创建system_logs表
     */
    async createSystemLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS system_logs (
    id VARCHAR(255) PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    context TEXT,
    module VARCHAR(100),
    user_id VARCHAR(255),
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('system_logs', sql);
    }

    /**
     * 创建system_maintenance表
     */
    async createSystemMaintenanceTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS system_maintenance (
    id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    maintenance_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'scheduled'::character varying,
    scheduled_start TIMESTAMP,
    scheduled_end TIMESTAMP,
    actual_start TIMESTAMP,
    actual_end TIMESTAMP,
    created_by VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
        `;
        await this.executeSQL('system_maintenance', sql);
    }

    /**
     * 创建user_activity_logs表
     */
    async createUserActivityLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_activity_logs (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    action VARCHAR(255) NOT NULL,
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_activity_logs', sql);
    }

    /**
     * 创建user_avatars表
     */
    async createUserAvatarsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_avatars (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    avatar_name VARCHAR(255),
    avatar_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    is_current BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_avatars', sql);
    }

    /**
     * 创建user_login_logs表
     */
    async createUserLoginLogsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_login_logs (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    username VARCHAR(255),
    login_time TIMESTAMP DEFAULT NOW(),
    logout_time TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(255),
    login_status VARCHAR(50) DEFAULT 'success'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_login_logs', sql);
    }

    /**
     * 创建user_notifications表
     */
    async createUserNotificationsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_notifications (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    type VARCHAR(50) DEFAULT 'info'::character varying,
    is_read BOOLEAN DEFAULT false,
    action_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    read_at TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_notifications', sql);
    }

    /**
     * 创建user_preferences表
     */
    async createUserPreferencesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_preferences (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) UNIQUE NOT NULL,
    preference_key VARCHAR(255) UNIQUE NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_preferences', sql);
    }

    /**
     * 创建user_profiles表
     */
    async createUserProfilesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_profiles (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    bio TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    avatar_url VARCHAR(500),
    timezone VARCHAR(100) DEFAULT 'Asia/Shanghai'::character varying,
    language VARCHAR(10) DEFAULT 'zh-CN'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_profiles', sql);
    }

    /**
     * 创建user_sessions表
     */
    async createUserSessionsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_token VARCHAR(500) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    last_activity TIMESTAMP DEFAULT NOW(),
    active BOOLEAN DEFAULT true,
    username VARCHAR(255),
    login_time TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_sessions', sql);
    }

    /**
     * 创建user_signatures表
     */
    async createUserSignaturesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_signatures (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    signature_name VARCHAR(255),
    signature_path VARCHAR(500),
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_signatures', sql);
    }

    /**
     * 创建user_tracking表
     */
    async createUserTrackingTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS user_tracking (
    id VARCHAR(50) PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('user_tracking', sql);
    }

    /**
     * 创建users表
     */
    async createUsersTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS users (
    id VARCHAR(50) PRIMARY KEY,
    usercode VARCHAR(50) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    department VARCHAR(100),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    permissions JSONB DEFAULT '[]',
    has_signature BOOLEAN DEFAULT false,
    signature_path VARCHAR(500),
    signature_base64 TEXT,
    last_login_at TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('users', sql);
    }

    /**
     * 创建warehouse_finished_products表
     */
    async createWarehouseFinishedProductsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_finished_products (
    id VARCHAR(255) PRIMARY KEY,
    product_code VARCHAR(255) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    specification TEXT,
    unit VARCHAR(50),
    quantity INTEGER DEFAULT 0,
    location VARCHAR(255),
    batch_number VARCHAR(255),
    production_date DATE,
    expiry_date DATE,
    status VARCHAR(50) DEFAULT 'available'::character varying,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    boxes_per_unit INTEGER,
    pieces_per_box INTEGER,
    total_pieces INTEGER,
    current_stock INTEGER
);
        `;
        await this.executeSQL('warehouse_finished_products', sql);
    }

    /**
     * 创建warehouse_inventory_transactions表
     */
    async createWarehouseInventoryTransactionsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_inventory_transactions (
    id VARCHAR(255) PRIMARY KEY,
    transaction_type VARCHAR(50) NOT NULL,
    item_type VARCHAR(50) NOT NULL,
    item_id VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2),
    total_amount DECIMAL(12,2),
    operator VARCHAR(255),
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    transaction_id TEXT,
    unit TEXT,
    qrcode TEXT,
    reason TEXT,
    operator_id TEXT,
    order_number TEXT,
    supplier_info TEXT,
    customer_info TEXT,
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('warehouse_inventory_transactions', sql);
    }

    /**
     * 创建warehouse_materials表
     */
    async createWarehouseMaterialsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_materials (
    id VARCHAR(50) PRIMARY KEY,
    material_code VARCHAR(100) UNIQUE NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    material_type VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs'::character varying,
    current_stock DECIMAL(12,2) DEFAULT 0,
    min_stock DECIMAL(12,2) DEFAULT 0,
    max_stock DECIMAL(12,2),
    unit_price DECIMAL(12,2),
    supplier VARCHAR(200),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active'::character varying,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    supplier_code VARCHAR(255),
    supplier_name VARCHAR(255),
    safety_stock INTEGER DEFAULT 0
);
        `;
        await this.executeSQL('warehouse_materials', sql);
    }

    /**
     * 创建warehouse_products表
     */
    async createWarehouseProductsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_products (
    id VARCHAR(50) PRIMARY KEY,
    product_code VARCHAR(100) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_type VARCHAR(100),
    unit VARCHAR(20) DEFAULT 'pcs'::character varying,
    current_stock DECIMAL(12,2) DEFAULT 0,
    min_stock DECIMAL(12,2) DEFAULT 0,
    max_stock DECIMAL(12,2),
    unit_price DECIMAL(12,2),
    location VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active'::character varying,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
        `;
        await this.executeSQL('warehouse_products', sql);
    }

    /**
     * 创建warehouse_qrcodes表
     */
    async createWarehouseQrcodesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
    id VARCHAR(50) PRIMARY KEY,
    qrcode VARCHAR(200) UNIQUE NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id VARCHAR(50) NOT NULL,
    batch_number VARCHAR(100),
    quantity DECIMAL(12,2),
    generated_by VARCHAR(50) NOT NULL,
    generated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    used_at TIMESTAMP,
    used_by VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active'::character varying,
    expires_at TIMESTAMP,
    batch_info TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (generated_by) REFERENCES users (id),
    FOREIGN KEY (used_by) REFERENCES users (id)
);
        `;
        await this.executeSQL('warehouse_qrcodes', sql);
    }

    /**
     * 创建warehouse_transactions表
     */
    async createWarehouseTransactionsTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS warehouse_transactions (
    id VARCHAR(50) PRIMARY KEY,
    transaction_number VARCHAR(100) UNIQUE NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id VARCHAR(50) NOT NULL,
    quantity DECIMAL(12,2) NOT NULL,
    unit_price DECIMAL(12,2),
    total_amount DECIMAL(12,2),
    batch_number VARCHAR(100),
    qrcode VARCHAR(200),
    operator_id VARCHAR(50) NOT NULL,
    notes TEXT,
    transaction_date TIMESTAMP NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    FOREIGN KEY (operator_id) REFERENCES users (id)
);
        `;
        await this.executeSQL('warehouse_transactions', sql);
    }

    /**
     * 执行SQL语句
     */
    async executeSQL(tableName, sql) {
        try {
            await this.pool.query(sql);
            logger.info(`表 ${tableName} 创建/检查完成`);
        } catch (error) {
            logger.error(`创建表 ${tableName} 失败:`, error);
            throw error;
        }
    }

    /**
     * 验证表结构完整性
     */
    async validateTableStructure() {
        try {
            logger.info('开始验证表结构完整性...');

            const client = await this.pool.connect();

            // 获取所有表名
            const tablesResult = await client.query(`
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            `);

            const existingTables = tablesResult.rows.map(row => row.table_name);
            logger.info(`发现 ${existingTables.length} 个数据表`);

            // 定义期望的核心表
            const expectedCoreTables = [
                'users', 'applications', 'application_approvals', 'departments',
                'equipment', 'products', 'schedules', 'quality_reports',
                'permission_templates', 'migrations'
            ];

            // 检查核心表是否存在
            const missingTables = expectedCoreTables.filter(table => !existingTables.includes(table));
            if (missingTables.length > 0) {
                logger.warn(`缺少核心表: ${missingTables.join(', ')}`);
            } else {
                logger.info('所有核心表结构验证通过');
            }

            // 检查关键字段
            await this.validateCriticalFields(client);

            client.release();
            logger.info('表结构完整性验证完成');
        } catch (error) {
            logger.error('表结构验证失败:', error);
            throw error;
        }
    }

    /**
     * 验证关键字段
     */
    async validateCriticalFields(client) {
        try {
            // 检查users表的关键字段
            const usersColumns = await client.query(`
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'users' AND table_schema = 'public'
                ORDER BY ordinal_position
            `);

            const requiredUserFields = ['id', 'username', 'password', 'role', 'created_at', 'updated_at'];
            const existingUserFields = usersColumns.rows.map(row => row.column_name);

            const missingUserFields = requiredUserFields.filter(field => !existingUserFields.includes(field));
            if (missingUserFields.length > 0) {
                logger.warn(`users表缺少字段: ${missingUserFields.join(', ')}`);
            } else {
                logger.info('users表关键字段验证通过');
            }
        } catch (error) {
            logger.error('关键字段验证失败:', error);
            // 不抛出错误，只记录警告
        }
    }

    /**
     * 执行数据库迁移
     */
    async runMigrations() {
        try {
            logger.info('开始执行数据库迁移...');

            const MigrationManager = require('./migrationManager');
            const migrationManager = new MigrationManager();

            await migrationManager.runMigrations();
            await migrationManager.close();

            logger.info('数据库迁移执行完成');
        } catch (error) {
            logger.error('数据库迁移执行失败:', error);
            // 迁移失败不应该阻止系统启动，只记录错误
            logger.warn('系统将继续启动，但某些功能可能不可用');
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        try {
            await this.pool.end();
            logger.debug('数据库初始化器连接池已关闭');
        } catch (error) {
            logger.error('关闭数据库连接池失败:', error);
        }
    }
}

module.exports = DatabaseInitializer;
